import { ChangeDetectorRef, Directive, EmbeddedViewRef, Input, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { BaseDtoIfc } from '@lib/common/interfaces';
import { Subject, take } from 'rxjs';
import { EditableCellValue } from './editable.cell.types';
import { ICService } from '@lib/frontend/core/services/ics/inter.comm.service';
import { ICS_TOPICS } from '@lib/frontend/core/interfaces/ics.interfaces';
import { isEmpty } from 'lodash';


@Directive({
    selector: '[editableCell]'
})
export abstract class EditableCellDirective<IFC extends BaseDtoIfc> implements OnInit, OnDestroy {
   
    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    @Input('editableCell') editableCellTemplate!: TemplateRef<unknown>;
    @Input() context = {};
    @Input() eventHandlers: {[key: string]: (event: unknown) => void} = {};
    @Input() path!: string;
    @Input() row!: IFC;
    @Input() channel!: string;

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected embeddedViewRef?: EmbeddedViewRef<unknown>;

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected destroy$ = new Subject<void>();

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    constructor(
        protected ics: ICService,
        protected vcr: ViewContainerRef,
        protected cdr: ChangeDetectorRef,
    ) {}

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public ngOnInit() {
        this.vcr.clear();
        const mergedContext = {...this.context, eventHandlers: this.eventHandlers};
        const idx = this.vcr.length;   
        this.embeddedViewRef = this.vcr.createEmbeddedView(this.editableCellTemplate, mergedContext, {index: idx});
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();

        if (this.embeddedViewRef) {
            this.embeddedViewRef.destroy();
        }
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public get value(): EditableCellValue {
        return this.path?.split('.').reduce((o,i) => ((o && o[i])), this.row) as unknown as EditableCellValue;
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public get editable() {
        return true;
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public onModelChange(value: EditableCellValue) {
        const transformed = this.transform(value);

        !isEmpty(transformed) && 
            this.ics.channel(this.channel)
                .request(ICS_TOPICS.PATCH_ITEM_REQUEST, transformed)
                .pipe(take(1), finally(() => ))
                .subscribe();
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public abstract transform(value: EditableCellValue): IFC;
}
