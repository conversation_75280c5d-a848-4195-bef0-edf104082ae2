import { KeyValue } from '@angular/common';
import { ChangeDetectorRef, Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { BaseDtoIfc } from '@lib/common/interfaces';
import { EditableCellDirective } from '../editable.cell.directive';
import { ICService } from '@lib/frontend/core/services/ics/inter.comm.service';
import { EditableCellValue } from '../editable.cell.types';

@Directive({
    selector: '[editableSelectCell]',
    exportAs: 'editableSelectCell',
})
export class EditableSelectCellDirective<IFC extends BaseDtoIfc> extends EditableCellDirective<IFC> {
    /*******************************************************************************************************************
    * Private
    *******************************************************************************************************************/
    protected options!: Array<KeyValue<string, string>>;

    /*******************************************************************************************************************
     * Inputs
    *******************************************************************************************************************/
    @Input('editableSelectCell') override editableCellTemplate!: TemplateRef<unknown>;

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    constructor(
        protected override ics: ICService,
        protected override vcr: ViewContainerRef,
        protected override cdr: ChangeDetectorRef,
    ) {
        super(ics, vcr, cdr);
        this.setOptions();
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected setOptions(): void{ this.options = []; }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected getOptions(): Array<KeyValue<string, string>> { return this.options; }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public transform(value: EditableCellValue): IFC {
        return {uuid: this.row.uuid, [this.path]: value} as IFC;
    }
}
