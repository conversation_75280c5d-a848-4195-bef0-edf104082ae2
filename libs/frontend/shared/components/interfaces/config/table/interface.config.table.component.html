<base_table_menu [settings]="tableMenuSettings$|async" [icsChannel]="this.icsChannel" (OnColumnSelectionChanged$)="OnColumnSelectionChanged($event)" (OnColumnsOrderChanged$)="OnColumnsOrderChanged($event)" (OnColumnPinChanged$)="OnColumnPinChanged($event)"></base_table_menu>
<ng-container *ngIf='{datasource: datasource$ | async, settings: tableSettings$ | async, order : columnsOrder$| async} as table;'>
    <cdk-virtual-scroll-viewport [tvsItemSize]='48' class='TAG_table_container' *ngIf="table.datasource && table.settings && table.order">
        <mat-table tableColumnReorderable tableColumnResizable matSort [dataSource]='table.datasource' matSortActive="device" matSortDirection="desc" class='TAG_table' [order]="table.order" [settings]="table.settings" [trackBy]='table.datasource.trackByFn'>

            <ng-container matColumnDef='network'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='network' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=network].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order">
                    <ng-container
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None')) && ((element.network !== 'None') ? (({uuid: (element.network), list: networks$|async}) | dtoPipe: 'label' | nullishCoalesce : 'No access') : 'None') as network"
                        [editableSelectCell]="networkEditableCell" 
                        #dir="editableSelectCell" 
                        [context]="{data: element, dir: dir, value: network}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'network'"
                    ></ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #networkEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir" let-network="value">
                <ng-container *ngIf="dir.editable; else network_template">
                    <decoy_select
                        [options]="networkOptions"
                        [ngModel]="dir.value"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)'
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_select>
                </ng-container>
                <ng-template #network_template>
                    <div etip>{{ network }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='type'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='type' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=type].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <div etip>{{ element.type }}</div>
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef='iface'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='iface' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=iface].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <div etip>{{ element.iface }}</div>
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef='device'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='device' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=device].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <div class='TAG_device_cell'>
                        <device_color [colors]="(({uuid: (element.device), list: devices$|async }) | dtoPipe) | extract : 'settings.color' | as : colorsCast"></device_color>
                        <div class='cell_value' etip>{{ ({uuid: (element.device), list: devices$|async}) | dtoPipe: 'label' | nullishCoalesce : 'No access' }}</div>
                    </div>
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef='mode'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='mode' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=mode].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableSelectCell]="modeEditableCell" 
                        #dir="editableSelectCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'mode'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #modeEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else mode">
                    <decoy_select
                        [options]="NETWORK_INTERFACE_MODES_VALUES"
                        [ngModel]="dir.value ?? NETWORK_INTERFACE_MODES.STATIC"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)'
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_select>
                </ng-container>
                <ng-template #mode>
                    <div etip>{{ dir.value }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='ip_address'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='ip_address' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=ip_address].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))" 
                        [editableTextCell]="ipAddressEditableCell" 
                        #dir="editableTextCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element" 
                        [channel]="icsChannel"
                        [path]="'ip_address'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #ipAddressEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else ipAddress">
                    <decoy_text_input 
                        size='15' 
                        [ngModel]="dir.value" 
                        (ngModelChange)="dir.onModelChange($event)" 
                        [minlength]='7' 
                        [maxlength]='15' 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data) || (data.mode !== NETWORK_INTERFACE_MODES.STATIC)' 
                        pattern='^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$'>
                    </decoy_text_input>
                </ng-container>

                <ng-template #ipAddress>
                    <div etip>{{ dir.value }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='mask'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='mask' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=mask].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableTextCell]="maskEditableCell" 
                        #dir="editableTextCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element" 
                        [channel]="icsChannel"
                        [path]="'mask'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #maskEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else mask">
                    <decoy_text_input 
                        size='15' 
                        [ngModel]="dir.value" 
                        (ngModelChange)="dir.onModelChange($event)" 
                        [minlength]='7' 
                        [maxlength]='15' 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data) || (data.mode !== NETWORK_INTERFACE_MODES.STATIC)' 
                        pattern='^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$'>
                    </decoy_text_input>
                </ng-container>

                <ng-template #mask>
                    <div etip>{{ dir.value }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='gateway'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='gateway' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=gateway].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableTextCell]="gatewayEditableCell" 
                        #dir="editableTextCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element" 
                        [channel]="icsChannel"
                        [path]="'gateway'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #gatewayEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="!dir.editable; else gateway">
                    <decoy_text_input 
                        size='15' 
                        [ngModel]="dir.value" 
                        (ngModelChange)="dir.onModelChange($event)" 
                        [minlength]='7' 
                        [maxlength]='15' 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data) || (data.mode !== NETWORK_INTERFACE_MODES.STATIC)' 
                        pattern='^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$'>
                    </decoy_text_input>
                </ng-container>

                <ng-template #gateway>
                    <div etip>{{ data | json }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='enable_management'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='enable_management' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=enable_management].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableToggleCell]="enableManagementEditableCell" 
                        #dir="editableToggleCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element" 
                        [channel]="icsChannel"
                        [path]="'enable_management'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #enableManagementEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else enable_management">
                    <decoy_toggle_slide 
                        [ngModel]="dir.value" 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)' 
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_toggle_slide>
                </ng-container>
                <ng-template #enable_management>
                    <mat-icon>{{ dir.value ? 'done' : 'close' }}</mat-icon>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='force_redis'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='force_redis' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=force_redis].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableToggleCell]="forceRedisEditableCell" 
                        #dir="editableToggleCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element" 
                        [channel]="icsChannel"
                        [path]="'force_redis'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #forceRedisEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else force_redis">
                    <decoy_toggle_slide 
                        [ngModel]="dir.value" 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)' 
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_toggle_slide>
                </ng-container>
                <ng-template #force_redis>
                    <mat-icon>{{ dir.value ? 'done' : 'close' }}</mat-icon>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='enable_video_bridge'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='enable_video_bridge' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=enable_video_bridge].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableToggleCell]="enableVideoBridgeEditableCell" 
                        #dir="editableToggleCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'enable_video_bridge'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #enableVideoBridgeEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else enable_video_bridge">
                    <decoy_toggle_slide 
                        [ngModel]="dir.value" 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)' 
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_toggle_slide>
                </ng-container>
                <ng-template #enable_video_bridge>
                    <mat-icon>{{ dir.value ? 'done' : 'close' }}</mat-icon>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='management_ttl'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='management_ttl' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=management_ttl].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableTextCell]="managementTtlEditableCell" 
                        #dir="editableTextCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'management_ttl'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #managementTtlEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                    <ng-container *ngIf="dir.editable; else management_ttl">
                        <decoy_text_input 
                            [ngModel]="dir.value" 
                            (ngModelChange)="dir.onModelChange($event)" 
                            [type]="'number'"
                            [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)' 
                            [min]="1" 
                            [max]="128">
                        </decoy_text_input>
                    </ng-container>
                    <ng-template #management_ttl>
                        <div etip>{{ dir.value }}</div>
                    </ng-template>
            </ng-template>

            <ng-container matColumnDef='enable_nmos'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='enable_nmos' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=enable_nmos].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="((element.type !== NETWORK_INTERFACE_TYPE.PHYSICAL) || (element.bonding_interface === 'None'))"
                        [editableToggleCell]="enableNmosEditableCell" 
                        #dir="editableToggleCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'enable_nmos'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #enableNmosEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else enable_nmos">
                    <decoy_toggle_slide 
                        [ngModel]="dir.value" 
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)' 
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_toggle_slide>
                </ng-container>
                <ng-template #enable_nmos>
                    <mat-icon>{{ dir.value ? 'done' : 'close' }}</mat-icon>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='vlan_id'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='vlan_id' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=vlan_id].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="(element.type === NETWORK_INTERFACE_TYPE.VLAN)"
                        [editableTextCell]="vlanIdEditableCell" 
                        #dir="editableTextCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'vlan_id'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #vlanIdEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else vlan_id">
                    <decoy_text_input 
                        [ngModel]="dir.value" 
                        (ngModelChange)="dir.onModelChange($event)" 
                        [type]="'number'"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownInterface(data)' 
                        [min]="1" 
                        [max]="4096">
                    </decoy_text_input>
                </ng-container>
                <ng-template #vlan_id>
                    <div etip>{{ dir.value }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='vlan_interface'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='vlan_interface' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=vlan_interface].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container 
                        *ngIf="(element.type === NETWORK_INTERFACE_TYPE.VLAN) && (element.vlan_interface !== 'None') && (({uuid: (element.vlan_interface), list: interfaces$|async}) | dtoPipe: 'iface' | nullishCoalesce : '') as vlan_interface"
                        [editableSelectCell]="vlanInterfaceEditableCell" 
                        #dir="editableSelectCell" 
                        [context]="{data: element, dir: dir, value: vlan_interface}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'vlan_interface'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #vlanInterfaceEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir" let-interface="value">
                <ng-container *ngIf="dir.editable && ((data.vlan_interfaces_options || {}) | keyvalue) as options; else vlan_interface">
                    <decoy_select
                        [options]="options"
                        [ngModel]="dir.value"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownInterface(data)' 
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_select>
                </ng-container>
                <ng-template #vlan_interface>
                    <div etip>{{ interface }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='bonding_mode'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='bonding_mode' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=bonding_mode].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container
                        *ngIf="(element.type === NETWORK_INTERFACE_TYPE.PHYSICAL) && (element.bonding_mode !== NETWORK_INTERFACE_BONDING_MODE.NONE)"
                        [editableSelectCell]="bondingModeEditableCell" 
                        #dir="editableSelectCell" 
                        [context]="{data: element, dir: dir}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'bonding_mode'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #bondingModeEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir">
                <ng-container *ngIf="dir.editable; else bonding_mode">
                    <decoy_select
                        [options]="NETWORK_INTERFACE_BONDING_MODE_VALUES"
                        [ngModel]="dir.value"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)'
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_select>
                </ng-container>
                <ng-template #bonding_mode>
                    <div etip>{{ dir.value }}</div>
                </ng-template>
            </ng-template>

            <ng-container matColumnDef='bonding_interface'>
                <mat-header-cell *matHeaderCellDef mat-sort-header='bonding_interface' [columnOrder]="table.order">
                    <div etip>{{ table.settings.columns | extract : '{n}[name=bonding_interface].title' }}</div>
                </mat-header-cell>
                <mat-cell *matCellDef='table.datasource; let element' [columnOrder]="table.order" enableCopy>
                    <ng-container
                        *ngIf="(element.type === NETWORK_INTERFACE_TYPE.PHYSICAL) && (element.bonding_interface !== 'None') && (({uuid: (element.bonding_interface), list: interfaces$|async}) | dtoPipe: 'iface' | nullishCoalesce : '') as bonding_interface"
                        [editableSelectCell]="bondingInterfaceEditableCell"
                        #dir="editableSelectCell" 
                        [context]="{data: element, dir: dir, value: bonding_interface}" 
                        [row]="element"
                        [channel]="icsChannel"
                        [path]="'bonding_interface'">
                    </ng-container>
                </mat-cell>
            </ng-container>

            <ng-template #bondingInterfaceEditableCell let-data="data" let-eventHandlers="eventHandlers" let-dir="dir" let-interface="value">
                <ng-container *ngIf="dir.editable && ((data.bonding_interfaces_options || {}) | keyvalue) as options; else bonding_interface">
                    <decoy_select
                        [options]="options"
                        [ngModel]="dir.value"
                        [disabled]='((data | allow : ROLE_ACTIONS.EDIT : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG | async) === "FORBID") || isUnknownOrVM(data)'
                        (ngModelChange)="dir.onModelChange($event)">
                    </decoy_select>
                </ng-container>
                <ng-template #bonding_interface>
                    <div etip>{{ interface }}</div>
                </ng-template>
            </ng-template>

            <mat-header-row *matHeaderRowDef='displayedColumns$|async; sticky: true'></mat-header-row>
            <mat-row *matRowDef='table.datasource; let row; columns: displayedColumns$|async;' #permission=permission [permissionCache]='( row | allow : [ROLE_ACTIONS.VIEW,ROLE_ACTIONS.EDIT] : ROLE_SUBJECTS.NETWORK_INTERFACE_CONFIG  | async)'
                     (dblclick)='permission.isAllowed() && OnRowDblClick($event, row)'
                     (click)='OnRowClick($event,row)'
                     [ngClass]='{selected:(table.datasource.selected | async).includes(row[table.datasource.keyid])}'>
            </mat-row>

        </mat-table>
    </cdk-virtual-scroll-viewport>
</ng-container>
